{"name": "vaikunth-yoga-retreat", "version": "1.0.0", "description": "A comprehensive MERN stack website for a Himalayan yoga and wellness retreat.", "main": "index.js", "scripts": {"start": "cd backend && npm start", "start:backend": "cd backend && npm run start", "start:frontend": "cd frontend && npm run dev", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "prebuild": "npm install", "build": "concurrently \"npm run build:backend\" \"npm run build:frontend\"", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && concurrently \"npm run install:backend\" \"npm run install:frontend\"", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "test": "concurrently \"npm run test:backend\" \"npm run test:frontend\"", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm run lint", "deploy": "npm run build && npm run copy-frontend", "copy-frontend": "node ensure-public-dir.js && cp -r ./frontend/dist/* ./backend/public/"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"concurrently": "^9.1.2"}}